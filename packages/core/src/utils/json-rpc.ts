import { safeDestr } from 'destr'
import { goTry } from 'go-go-try'

export type JsonrpcRequest<TParams = unknown> = {
  jsonrpc: '2.0'
  id: string
  method: string
  params?: TParams
}

export type JsonrpcNotification<TParams = unknown> = {
  jsonrpc: '2.0'
  method: string
  params?: TParams
}

export type JsonrpcSuccessResponse<TResult = unknown> = {
  jsonrpc: '2.0'
  id: string
  result: TResult
}

export type JsonrpcErrorResponse<TErrorData = unknown> = {
  jsonrpc: '2.0'
  id: string
  error: {
    code: number
    message: string
    data?: TErrorData
  }
}

export type JsonrpcResponse<T = unknown> = JsonrpcSuccessResponse<T> | JsonrpcErrorResponse<T>
export type JsonrpcMessage<T = unknown> = JsonrpcRequest<T> | JsonrpcNotification<T> | JsonrpcResponse<T>

export function jsonrpcRequest<T>(body: Omit<JsonrpcRequest<T>, 'jsonrpc'>): JsonrpcRequest<T> {
  return { jsonrpc: '2.0', ...body }
}

export function jsonrpcNotification<T>(body: Omit<JsonrpcNotification<T>, 'jsonrpc'>): JsonrpcNotification<T> {
  return { jsonrpc: '2.0', ...body }
}

export function jsonrpcSuccessRespone<T>(body: Omit<JsonrpcSuccessResponse<T>, 'jsonrpc'>): JsonrpcSuccessResponse<T> {
  return { jsonrpc: '2.0', ...body }
}

export function jsonrpcErrorResponse<T>(body: Omit<JsonrpcErrorResponse<T>, 'jsonrpc'>): JsonrpcErrorResponse<T> {
  return { jsonrpc: '2.0', ...body }
}

export function isJsonrpcRequest(message: JsonrpcMessage): message is JsonrpcRequest {
  return 'id' in message && 'method' in message
}

export function isJsonrpcNotification(message: JsonrpcMessage): message is JsonrpcNotification {
  return !('id' in message) && 'method' in message
}

export function isJsonrpcResponse(message: JsonrpcMessage): message is JsonrpcResponse {
  return isJsonrpcSuccessResponse(message) || isJsonrpcErrorResponse(message)
}

export function isJsonrpcSuccessResponse(message: JsonrpcMessage): message is JsonrpcSuccessResponse {
  return 'id' in message && 'result' in message && !('error' in message)
}

export function isJsonrpcErrorResponse(message: JsonrpcMessage): message is JsonrpcErrorResponse {
  return 'id' in message && 'error' in message && !('result' in message)
}

export function parseJsonrpcMessage(response: unknown) {
  const [err, result] = goTry(() => safeDestr<JsonrpcMessage>(response))
  if (err || typeof result !== 'object' || result === null || Array.isArray(result)) {
    throw new Error('Invalid json-rpc message')
  }

  if (result.jsonrpc !== '2.0') {
    throw new Error('Invalid json-rpc message')
  }

  if (!isJsonrpcRequest(result) && !isJsonrpcNotification(result) && !isJsonrpcResponse(result)) {
    throw new Error('Invalid json-rpc message')
  }

  return result
}
