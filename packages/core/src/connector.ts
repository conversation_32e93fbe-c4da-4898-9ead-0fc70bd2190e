import ExpiryMap from 'expiry-map'
import EventEmitter from 'eventemitter3'

import {
  parseJsonrpcMessage,
  isJsonrpcNotification,
  isJsonrpcErrorResponse,
  type <PERSON>sonrpcRequest,
  type <PERSON><PERSON>rpcNotification,
  type <PERSON>sonrpcResponse,
} from './utils/json-rpc'

interface PromiseActions<TValue = unknown> {
  resolve(value: TValue): void
  reject(reason?: any): void
}

export interface WebshellConnectorInitOptions {
  /**
   * A `expiry-map` instance.
   *
   * @default new ExpiryMap(15_000)
   */
  promiseStore?: ExpiryMap<string, PromiseActions>

  /**
   * A `eventemitter3` instance.
   */
  eventEmitter?: EventEmitter

  /**
   * RPC request timeout in milliseconds.
   *
   * Set to negative to disable timeout.
   *
   * @default 10_000
   */
  requestTimeout?: number
}

const DEFAULT_CACHE_TTL = 15_000
const DEFAULT_REQUEST_TIMEOUT = 10_000

export class WebshellConnector extends WebSocket {
  private promiseStore: ExpiryMap<string, PromiseActions>
  private eventEmitter: EventEmitter
  private readonly requestTimeout: number

  constructor(url: string | URL, protocols?: string | string[], options?: WebshellConnectorInitOptions) {
    super(url, protocols)

    this.promiseStore = options?.promiseStore ?? new ExpiryMap(DEFAULT_CACHE_TTL)
    this.eventEmitter = options?.eventEmitter ?? new EventEmitter()
    this.requestTimeout = options?.requestTimeout ?? DEFAULT_REQUEST_TIMEOUT

    this.addEventListener('message', (event: MessageEvent<string>) => {
      const message = parseJsonrpcMessage(event.data)

      if (isJsonrpcNotification(message)) {
        this.eventEmitter.emit(message.method, message.params)
        return
      }

      const promise = this.promiseStore.get(message.id)

      if (promise) {
        this.promiseStore.delete(message.id)
        if (isJsonrpcErrorResponse(message)) {
          promise.reject(new Error(message.error.message))
        } else {
          promise.resolve(message.result)
        }
      }
    })

    this.addEventListener('close', () => {
      for (const promise of this.promiseStore.values()) {
        promise.reject(new Error('Connection closed'))
      }
    })
  }

  sendRequest<TParams, TResult>(request: JsonrpcRequest<TParams>) {
    return new Promise<JsonrpcResponse<TResult>>((resolve, reject) => {
      this.promiseStore.set(request.id, { resolve, reject })
      this.send(JSON.stringify(request))
      setTimeout(() => reject(new Error(`Request timeout: ${request.id}`)), this.requestTimeout)
    })
  }

  sendNotification<TParams>(notification: JsonrpcNotification<TParams>) {
    this.send(JSON.stringify(notification))
  }
}
